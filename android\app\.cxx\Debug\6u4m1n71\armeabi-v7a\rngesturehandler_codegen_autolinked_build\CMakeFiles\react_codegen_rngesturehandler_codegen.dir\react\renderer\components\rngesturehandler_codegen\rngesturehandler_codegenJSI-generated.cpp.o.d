rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o: \
  C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp \
  C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/ReactCommon/TurboModule.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/memory \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__assert \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__config \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__config_site \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/features.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/sys/cdefs.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/android/versioning.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/android/api-level.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/get_device_api_level_inlines.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/android/ndk-version.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__verbose_abort \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__availability \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/addressof.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/align.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/cstddef \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/enable_if.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/integral_constant.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_integral.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/remove_cv.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/remove_const.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/remove_volatile.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/version \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/stddef.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include/stddef.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include/__stddef_ptrdiff_t.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include/__stddef_size_t.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include/__stddef_wchar_t.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include/__stddef_null.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include/__stddef_nullptr_t.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include/__stddef_max_align_t.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include/__stddef_offsetof.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/allocate_at_least.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/allocator_traits.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/construct_at.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/access.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/voidify.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_array.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/declval.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/forward.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_reference.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/remove_reference.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/move.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/conditional.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_copy_constructible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/add_const.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/add_lvalue_reference.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_referenceable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_same.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_nothrow_move_constructible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/add_rvalue_reference.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_nothrow_constructible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_constructible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__undef_macros \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/new \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__exception/exception.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_function.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_const.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/cstdlib \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/stdlib.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/stdlib.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/alloca.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/wait.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/linux/wait.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/malloc.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/stdio.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/stdio.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/sys/types.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/stdint.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include/stdint.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/stdint.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/wchar_limits.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/linux/types.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/arm-linux-androideabi/asm/types.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/asm-generic/int-ll64.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/arm-linux-androideabi/asm/bitsperlong.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/asm-generic/bitsperlong.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/linux/posix_types.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/linux/stddef.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/linux/compiler_types.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/linux/compiler.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/arm-linux-androideabi/asm/posix_types.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/asm-generic/posix_types.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/pthread_types.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include/stdarg.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include/__stdarg___gnuc_va_list.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include/__stdarg_va_list.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include/__stdarg_va_arg.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include/__stdarg___va_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include/__stdarg_va_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/seek_constants.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/fortify/stdio.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/xlocale.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/fortify/stdlib.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/android/legacy_stdlib_inlines.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/exception \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__exception/exception_ptr.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__exception/operations.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__exception/nested_exception.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/decay.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/add_pointer.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_void.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/remove_extent.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_base_of.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_class.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_convertible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_final.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_polymorphic.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__exception/terminate.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/type_traits \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__fwd/hash.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/add_cv.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/add_volatile.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/aligned_storage.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/nat.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/type_list.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/aligned_union.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/alignment_of.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/apply_cv.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_volatile.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/can_extract_key.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__fwd/pair.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/remove_const_ref.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/common_reference.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/common_type.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/remove_cvref.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/void_t.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/copy_cv.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/copy_cvref.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/conjunction.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/dependent_type.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/disjunction.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/extent.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/has_unique_object_representation.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/remove_all_extents.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/has_virtual_destructor.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/invoke.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_core_convertible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_member_function_pointer.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_member_object_pointer.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_reference_wrapper.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_abstract.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_aggregate.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_arithmetic.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_floating_point.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_assignable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_bounded_array.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_callable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_char_like_type.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_standard_layout.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_trivial.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_compound.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_fundamental.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_null_pointer.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_constant_evaluated.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_copy_assignable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_default_constructible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_destructible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_empty.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_enum.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_implicitly_default_constructible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_literal_type.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_member_pointer.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_move_assignable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_move_constructible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_nothrow_assignable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_nothrow_convertible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/lazy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_nothrow_copy_assignable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_nothrow_copy_constructible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_nothrow_default_constructible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_nothrow_destructible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_scalar.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_pointer.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_nothrow_move_assignable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_object.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_union.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_pod.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_scoped_enum.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/underlying_type.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_signed.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_specialization.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_swappable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_trivially_assignable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_trivially_constructible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_trivially_copy_assignable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_trivially_copy_constructible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_trivially_copyable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/cstdint \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_trivially_default_constructible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_trivially_destructible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_trivially_move_assignable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_trivially_move_constructible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_unbounded_array.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_unsigned.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/make_const_lvalue_ref.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/make_signed.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/make_unsigned.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/maybe_const.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/negation.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/rank.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/remove_pointer.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/result_of.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/invoke.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/type_identity.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/unwrap_ref.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/pointer_traits.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/limits \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/allocation_guard.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/allocator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/allocator_arg_t.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/uses_allocator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/assume_aligned.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/auto_ptr.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/compressed_pair.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__fwd/get.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/copyable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/assignable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/common_reference_with.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/convertible_to.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/same_as.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/constructible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/destructible.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/movable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/swappable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/class_or_enum.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/exchange.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/swap.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__fwd/array.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__fwd/subrange.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/concepts.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/arithmetic.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_signed_integer.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_unsigned_integer.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/derived_from.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/equality_comparable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/boolean_testable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/invocable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/predicate.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/regular.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/semiregular.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/relation.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/totally_ordered.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/incrementable_traits.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_primary_template.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_valid_expansion.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/iter_move.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/iterator_traits.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/readable_traits.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__fwd/tuple.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__tuple/tuple_element.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__tuple/tuple_indices.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/integer_sequence.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__tuple/tuple_types.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/piecewise_construct.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/concepts.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__ranges/access.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__ranges/enable_borrowed_range.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/auto_cast.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__ranges/concepts.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__ranges/data.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__ranges/enable_view.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__ranges/size.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/initializer_list \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/ranges_construct_at.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__ranges/dangling.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/ranges_uninitialized_algorithms.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/in_out_result.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/uninitialized_algorithms.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/copy_move_common.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/iterator_operations.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/iter_swap.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_iterator_concept.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/advance.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/convert_to_integral.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/unreachable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/distance.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/iter_swap.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/next.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/prev.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/unwrap_iter.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/unwrap_range.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/pair.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__compare/common_comparison_category.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__compare/ordering.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__compare/synth_three_way.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__compare/three_way_comparable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/different_from.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__tuple/pair_like.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__tuple/tuple_like.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__tuple/tuple_size.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__tuple/sfinae_helpers.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__tuple/make_tuple_types.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__tuple/tuple_like_ext.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__string/constexpr_c_functions.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/datasizeof.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_always_bitcastable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_equality_comparable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_trivially_lexicographically_comparable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/is_pointer_in_range.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/comp.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/operation_traits.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/for_each_segment.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/segmented_iterator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/min.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/comp_ref_type.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/min_element.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/identity.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/reference_wrapper.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/weak_result_type.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/binary_function.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/unary_function.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/move.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/reverse_iterator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__compare/compare_three_way_result.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/iterator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__ranges/subrange.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__ranges/view_interface.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__ranges/empty.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/exception_guard.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/raw_storage_iterator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/shared_ptr.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__compare/compare_three_way.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/operations.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__fwd/ostream.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__fwd/string.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__fwd/memory_resource.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/allocator_destructor.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/unique_ptr.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/hash.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/cstring \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/string.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/string.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/strcasecmp.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/strings.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/fortify/strings.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/fortify/string.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/typeinfo \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__atomic/memory_order.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/temporary_buffer.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/uses_allocator_construction.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/tuple \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/compare \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__compare/compare_partial_order_fallback.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__compare/partial_order.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__compare/weak_order.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__compare/strong_order.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__bit/bit_cast.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/priority_tag.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/cmath \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/promote.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/math.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/math.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include/limits.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/limits.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/float.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include/float.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/linux/limits.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/posix_limits.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/abs.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/copysign.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/error_functions.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/exponential_functions.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/fdim.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/fma.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/gamma.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/hyperbolic_functions.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/hypot.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/inverse_hyperbolic_functions.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/inverse_trigonometric_functions.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/logarithms.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/min_max.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/modulo.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/remainder.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/roots.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/rounding_functions.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/traits.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__math/trigonometric_functions.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__compare/compare_strong_order_fallback.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__compare/compare_weak_order_fallback.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__compare/is_eq.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/iosfwd \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__fwd/fstream.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__fwd/ios.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__fwd/istream.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__fwd/sstream.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__fwd/streambuf.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__std_mbstate_t.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__mbstate_t.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/wchar.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/time.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/sys/time.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/linux/time.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/timespec.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/linux/time_types.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/sys/select.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/signal.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/arm-linux-androideabi/asm/sigcontext.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/signal_types.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/linux/signal.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/arm-linux-androideabi/asm/signal.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/asm-generic/signal-defs.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/arm-linux-androideabi/asm/siginfo.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/asm-generic/siginfo.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/sys/ucontext.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/sys/user.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/page_size.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/bionic_multibyte_result.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/mbstate_t.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/wctype.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/utility \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/as_const.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/as_lvalue.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/cmp.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/forward_like.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/in_place.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/rel_ops.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/to_underlying.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/atomic \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__atomic/aliases.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__atomic/atomic.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__atomic/atomic_base.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__atomic/atomic_sync.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__atomic/contention_t.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__atomic/cxx_atomic_impl.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__atomic/is_always_lock_free.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/duration.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/ratio \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/climits \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__thread/poll_with_backoff.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/high_resolution_clock.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/steady_clock.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/time_point.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/system_clock.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/ctime \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__threading_support \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/convert_to_timespec.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/errno.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/errno.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/linux/errno.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/arm-linux-androideabi/asm/errno.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/asm-generic/errno.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/asm-generic/errno-base.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/pthread.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/sched.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/linux/sched.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__atomic/check_memory_order.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__atomic/atomic_lock_free.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__atomic/atomic_flag.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__atomic/atomic_init.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__atomic/fence.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__atomic/kill_dependency.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/concepts \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__concepts/common_with.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/iterator \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/back_insert_iterator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/bounded_iter.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/common_iterator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/variant \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__variant/monostate.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/counted_iterator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/default_sentinel.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/data.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/empty.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/erase_if_container.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/front_insert_iterator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/indirectly_comparable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/projected.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/insert_iterator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/istream_iterator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/istreambuf_iterator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/mergeable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/ranges_operations.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/move_iterator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/move_sentinel.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/ostream_iterator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/ostreambuf_iterator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/permutable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/reverse_access.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/size.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/sortable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/unreachable_sentinel.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/wrap_iter.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/stdexcept \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/string \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/max.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/max_element.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/remove.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/find.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/find_segment_if.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__bit/countr.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__bit/rotate.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__bit/invert_if.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__fwd/bit_reference.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/cwchar \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/cwctype \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/cctype \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/ctype.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/ctype.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/wctype.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/wctype.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/wchar.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/find_if.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/remove_if.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/enable_insertable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__ios/fpos.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/swap_allocator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory_resource/polymorphic_allocator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory_resource/memory_resource.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__ranges/container_compatible_range.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__ranges/from_range.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__string/char_traits.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/copy_n.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/fill_n.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/find_end.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/search.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/find_first_of.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/cstdio \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__string/extern_template_lists.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_allocator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/noexcept_move_assign_container.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/string_view \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__fwd/string_view.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/algorithm \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/adjacent_find.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/all_of.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/any_of.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/binary_search.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/lower_bound.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/half_positive.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/clamp.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/copy_backward.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/copy_if.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/count.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__bit/popcount.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/count_if.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/equal.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/equal_range.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/upper_bound.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/fill.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/find_if_not.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/fold.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/for_each.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__ranges/movable_box.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/optional \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/for_each_n.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/generate.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/generate_n.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/in_found_result.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/in_fun_result.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/in_in_out_result.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/in_in_result.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/in_out_out_result.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/includes.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/inplace_merge.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/rotate.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/move_backward.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/swap_ranges.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/destruct_n.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/is_heap.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/is_heap_until.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/is_partitioned.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/is_permutation.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/is_sorted.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/is_sorted_until.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/lexicographical_compare.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/lexicographical_compare_three_way.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/three_way_comp_ref_type.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/make_heap.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/sift_down.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/merge.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/min_max_result.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/minmax.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/minmax_element.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/mismatch.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/next_permutation.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/reverse.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/none_of.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/nth_element.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/sort.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/partial_sort.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/sort_heap.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pop_heap.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/push_heap.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__debug_utils/strict_weak_ordering_check.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__debug_utils/randomize_range.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__bit/blsr.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__bit/countl.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/partial_sort_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/make_projected.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/partition.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/partition_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/partition_point.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/prev_permutation.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_any_all_none_of.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_find.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_backend.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_backends/cpu_backend.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_backends/cpu_backends/any_of.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_backends/cpu_backends/backend.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_backends/cpu_backends/thread.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/empty.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/is_execution_policy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_backends/cpu_backends/fill.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_backends/cpu_backends/find_if.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_backends/cpu_backends/for_each.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_backends/cpu_backends/merge.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_backends/cpu_backends/stable_sort.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/stable_sort.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_backends/cpu_backends/transform.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/transform.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_backends/cpu_backends/transform_reduce.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__numeric/transform_reduce.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/execution \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_frontend_dispatch.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/cpp17_iterator_concepts.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_transform.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_count.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_for_each.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__numeric/pstl_transform_reduce.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_equal.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_fill.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_generate.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_is_partitioned.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_merge.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_move.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_replace.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_rotate_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_sort.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/pstl_stable_sort.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_adjacent_find.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_all_of.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_any_of.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_binary_search.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_clamp.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_contains.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_find.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_find_if.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_copy_backward.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_copy_if.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_copy_n.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_count.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_count_if.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_ends_with.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_equal.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_starts_with.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_mismatch.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_equal_range.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_fill.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_fill_n.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_find_end.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_find_first_of.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_find_if_not.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_for_each.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_for_each_n.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_generate.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_generate_n.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_includes.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_inplace_merge.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_is_heap.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_is_heap_until.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_is_partitioned.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_is_permutation.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_is_sorted.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_is_sorted_until.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_lexicographical_compare.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_lower_bound.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_make_heap.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_max.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_min_element.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_max_element.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_merge.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_min.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_minmax.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_minmax_element.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_move.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_move_backward.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_next_permutation.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_none_of.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_nth_element.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_partial_sort.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_partial_sort_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_partition.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_partition_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_partition_point.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_pop_heap.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_prev_permutation.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_push_heap.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_remove.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_remove_if.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_remove_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_remove_copy_if.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/remove_copy_if.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_replace.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_replace_if.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_replace_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_replace_copy_if.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_reverse.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_reverse_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_rotate.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_rotate_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_sample.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/sample.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__random/uniform_int_distribution.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__random/is_valid.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__random/log2.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/uniform_random_bit_generator_adaptor.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__random/uniform_random_bit_generator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_search.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_search_n.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/search_n.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_set_difference.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/set_difference.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_set_intersection.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/set_intersection.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_set_symmetric_difference.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/set_symmetric_difference.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_set_union.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/set_union.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_shuffle.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/shuffle.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_sort.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_sort_heap.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_stable_partition.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/stable_partition.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_stable_sort.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_swap_ranges.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_transform.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_unique.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/unique.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_unique_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/unique_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/ranges_upper_bound.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/remove_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/replace.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/replace_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/replace_copy_if.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/replace_if.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/reverse_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/rotate_copy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/shift_left.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__algorithm/shift_right.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/bit \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__bit/bit_ceil.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__bit/bit_floor.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__bit/bit_log2.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__bit/bit_width.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__bit/byteswap.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__bit/endian.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__bit/has_single_bit.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/unordered_map \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/is_transparent.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__hash_table \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/ranges_iterator_traits.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__node_handle \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include/jsi/jsi.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/cassert \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/assert.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/functional \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/binary_negate.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/bind.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/bind_back.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/perfect_forward.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/bind_front.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/binder1st.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/binder2nd.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/boyer_moore_searcher.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/array \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/vector \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__bit_reference \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/formatter.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/format_fwd.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/formatter_bool.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/concepts.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/format_parse_context.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/format_error.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/formatter_integral.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__charconv/to_chars_integral.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__charconv/tables.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__charconv/to_chars_base_10.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__charconv/to_chars_result.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__system_error/errc.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/cerrno \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__charconv/traits.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/make_32_64_or_128_bit.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/formatter_output.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/buffer.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/format_to_n_result.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/parser_std_format_spec.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/format_arg.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/format_string.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/unicode.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/extended_grapheme_cluster_table.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/width_estimation_table.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/locale \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__locale \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__mutex/once_flag.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/no_destroy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/clocale \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/locale.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/locale.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__support/android/locale_bionic.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/ios \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__system_error/error_category.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__system_error/error_code.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__system_error/error_condition.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__system_error/system_error.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/mutex \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__condition_variable/condition_variable.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__mutex/mutex.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__mutex/unique_lock.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__mutex/tag_types.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__mutex/lock_guard.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__thread/id.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/system_error \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/streambuf \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__locale_dir/locale_base_api/bsd_locale_fallbacks.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__locale_dir/locale_base_api/locale_guard.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/cstdarg \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/temp_value.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__split_buffer \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/compose.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/default_searcher.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/function.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/builtin_new_allocator.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__type_traits/strip_signature.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/mem_fn.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/mem_fun_ref.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/not_fn.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/pointer_to_binary_function.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/pointer_to_unary_function.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/unary_negate.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include/jsi/jsi-inl.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/ReactCommon/CallInvoker.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/ReactCommon/SchedulerPriority.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/chrono \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/calendar.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/convert_to_tm.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/concepts.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/hh_mm_ss.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/day.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/file_clock.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/month.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/month_weekday.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/weekday.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/monthday.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/statically_widen.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/year.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/year_month.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/year_month_day.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/year_month_weekday.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/literals.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/formatter.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/ostream.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/format_functions.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/format_arg_store.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/format_args.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/format_context.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/formatter_char.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/write_escaped.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/escaped_output_table.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/formatter_floating_point.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__charconv/chars_format.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__charconv/to_chars_floating_point.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/formatter_integer.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/formatter_pointer.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__format/formatter_string.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/ostream \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/bitset \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/print \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/unistd.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/fcntl.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/getentropy.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/getopt.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/ioctl.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/lockf.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/sysconf.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/fortify/unistd.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/android/legacy_unistd_inlines.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/bits/swab.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__chrono/parser_std_format_spec.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/sstream \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/istream \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/forward_list \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/charconv \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__charconv/from_chars_integral.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__charconv/from_chars_result.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__charconv/to_chars.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/bridging/EventEmitter.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/bridging/Function.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/bridging/Base.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/bridging/Convert.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/bridging/CallbackWrapper.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/bridging/LongLivedObject.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/unordered_set \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/bridging/Bridging.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/bridging/AString.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/bridging/Array.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/deque \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/list \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/set \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__tree \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/bridging/Bool.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/bridging/Class.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/bridging/Dynamic.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/dynamic.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/json/dynamic.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/Expected.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/CPortability.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/portability/Config.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/CppAttributes.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/Portability.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/Likely.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/Builtin.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/Preprocessor.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/Traits.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/Unit.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/Utility.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/Exception.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/fmt/format.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/fmt/base.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/Assume.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/Hint.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/SafeAssert.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/CArray.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/Hint-inl.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/Thunk.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/New.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/functional/Invoke.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/control/expr_iif.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/config/config.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/facilities/is_empty_variadic.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/punctuation/is_begin_parens.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/punctuation/detail/is_begin_parens.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/facilities/detail/is_empty.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/variadic/has_opt.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/variadic/detail/has_opt.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/list/for_each.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/list/for_each_i.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/arithmetic/inc.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/config/limits.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/arithmetic/limits/inc_256.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/list/adt.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/detail/is_binary.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/detail/check.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/cat.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/logical/compl.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/tuple/eat.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/repetition/for.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/arithmetic/dec.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/arithmetic/limits/dec_256.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/debug/error.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/facilities/empty.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/logical/bool.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/logical/limits/bool_256.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/detail/auto_rec.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/control/iif.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/detail/limits/auto_rec_256.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/repetition/detail/for.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/repetition/detail/limits/for_256.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/repetition/limits/for_256.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/tuple/elem.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/facilities/expand.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/facilities/overload.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/variadic/size.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/facilities/check_empty.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/variadic/limits/size_64.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/tuple/rem.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/tuple/detail/is_single_return.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/variadic/elem.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/variadic/limits/elem_64.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/logical/not.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/tuple/to_list.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/control/if.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/tuple/size.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/boost/preprocessor/tuple/limits/to_list_64.hpp \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/CustomizationPoint.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/StaticConst.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/TypeInfo.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/Range.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/hash/SpookyHashV2.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/CString.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/portability/Constexpr.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/CpuId.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/detail/RangeCommon.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/detail/RangeSse42.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/container/Access.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/container/F14Map.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/container/View.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/container/F14Map-fwd.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/container/detail/F14Defaults.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/container/HeterogeneousAccess-fwd.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/memory/MemoryResource.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/memory_resource \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory_resource/monotonic_buffer_resource.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory_resource/pool_options.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory_resource/synchronized_pool_resource.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory_resource/unsynchronized_pool_resource.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/container/Iterator.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/RValueReferenceWrapper.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/container/detail/F14MapFallback.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/Optional.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/hash/traits.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/container/detail/F14Table.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/Bits.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/Bits.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/cinttypes \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/inttypes.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include/inttypes.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/inttypes.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/ConstexprMath.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/CheckedMath.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/portability/Builtins.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/Memory.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/Align.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/memory/Malloc.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/portability/Malloc.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/memory/detail/MallocImpl.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/ScopeGuard.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/UncaughtExceptions.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/Pretty.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/container/HeterogeneousAccess.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/hash/Hash.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/functional/ApplyTuple.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/hash/MurmurHash.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/hash/SpookyHashV1.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/container/detail/F14IntrinsicsAvailability.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/container/detail/F14Mask.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/container/detail/Util.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/container/detail/F14Policy.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/json_pointer.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/json/json_pointer.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/json/dynamic-inl.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/Conv.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/double-conversion/double-conversion.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/double-conversion/utils.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/Demangle.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/FBString.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/lang/ToAscii.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/portability/Math.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/Format.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/FormatArg.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/String.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/ExceptionString.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/detail/SimpleSimdStringUtils.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/detail/SplitStringSimd.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/String-inl.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/container/Reserve.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/Format-inl.h \
  D:/AndroidSdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/map \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/Exception.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/portability/SysTypes.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/FormatTraits.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/MapUtil.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/container/MapUtil.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/portability/Windows.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/folly/detail/Iterators.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include/jsi/JSIDynamic.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/bridging/Error.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/bridging/Number.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/bridging/Object.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/bridging/Promise.h \
  C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/bridging/Value.h
